"""
Shutdown Overlay Window for Auto Shutdown Application
Runs as a separate process to show the shutdown warning overlay
"""

import webview
import sys
import os
import threading
import time
import subprocess
from config_manager import ConfigManager


class ShutdownOverlayAPI:
    def __init__(self):
        self.config_manager = ConfigManager()
        config = self.config_manager.load_config()
        self.countdown_duration = config.get('settings', {}).get('countdown_duration', 30)
        self.countdown_thread = None
        self.is_active = True
        self.window_ready = False

        # 不立即启动倒计时，等待窗口准备完成
        print(f"Shutdown overlay initialized with {self.countdown_duration}s countdown")
    
    def get_countdown_duration(self):
        """Get countdown duration for JavaScript"""
        return self.countdown_duration

    def window_loaded(self):
        """Called when the window is fully loaded and ready"""
        print("Window loaded, starting countdown...")
        self.window_ready = True
        self.start_countdown()
        return {"success": True}
    
    def cancel_shutdown(self):
        """Cancel shutdown (called from JavaScript)"""
        print("Shutdown cancelled by user")
        self.is_active = False
        # Close the window
        try:
            webview.windows[0].destroy()
        except:
            pass
        return {"success": True}
    
    def start_countdown(self):
        """Start the countdown timer"""
        def countdown():
            for remaining in range(self.countdown_duration, 0, -1):
                if not self.is_active:
                    return
                
                # Update countdown display
                try:
                    webview.windows[0].evaluate_js(f'updateCountdown({remaining})')
                except:
                    pass
                
                time.sleep(1)
            
            # Time's up - execute shutdown
            if self.is_active:
                self.execute_shutdown()
        
        self.countdown_thread = threading.Thread(target=countdown, daemon=True)
        self.countdown_thread.start()
    
    def execute_shutdown(self):
        """Execute system shutdown"""
        print("Executing shutdown...")
        try:
            if sys.platform == "win32":
                subprocess.run(['shutdown', '/s', '/f', '/t', '0'], check=True)
            elif sys.platform in ["linux", "darwin"]:
                subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=True)
            else:
                print(f"Unsupported platform: {sys.platform}")
        except subprocess.CalledProcessError as e:
            print(f"Failed to shutdown: {e}")
        except FileNotFoundError:
            print("Shutdown command not found - running in test mode")


def main():
    """Run the shutdown overlay window"""
    try:
        # Create API instance
        api = ShutdownOverlayAPI()

        # Get the correct path to the HTML file
        base_path = os.path.dirname(os.path.abspath(__file__))
        html_path = os.path.join(base_path, 'static', 'shutdown_overlay.html')

        # Verify the HTML file exists
        if not os.path.exists(html_path):
            print(f"HTML file not found: {html_path}")
            sys.exit(1)

        print(f"Loading shutdown overlay from: {html_path}")

        # Create fullscreen overlay window with optimized settings
        window = webview.create_window(
            'Shutdown Warning',
            html_path,
            fullscreen=True,
            on_top=True,
            shadow=False,
            resizable=False,
            js_api=api,
            # 优化设置减少白屏
            minimized=False,
            focus=True,
            # 设置初始背景色
            background_color='#1a1a2e'
        )

        # Start webview with optimized settings
        webview.start(
            debug=False,  # 关闭debug模式提高性能
            http_server=False,  # 使用文件协议提高加载速度
            private_mode=False
        )

    except Exception as e:
        print(f"Shutdown overlay error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
