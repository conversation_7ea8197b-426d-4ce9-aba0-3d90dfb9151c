<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动关机设置</title>
    <style>
        :root {
            --background-color: #f5f7fa;
            --surface-color: #ffffff;
            --text-primary: #1f2a44;
            --text-secondary: #64748b;
            --border-color: #e5e7eb;
            --primary-color: #3b82f6;
            --success-color: #10b981;
            --enabled-bg: #d1fae5;
            --enabled-text: #065f46;
            --disabled-bg: #fee2e2;
            --disabled-text: #991b1b;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            min-height: 100vh;
            display: grid;
            place-items: center;
            padding: 16px;
            line-height: 1.5;
        }
        .container {
            max-width: 600px;
            width: 100%;
            background-color: var(--surface-color);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }
        header {
            margin-bottom: 20px;
            text-align: left;
        }
        header h1 {
            font-size: 1.6rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        header p {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 4px;
        }
        .schedules-section {
            margin-bottom: 20px;
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .section-header h2 {
            font-size: 1.2rem;
            font-weight: 600;
        }
        .schedules-list {
            display: grid;
            gap: 10px;
        }
        .schedule-item {
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 12px;
            display: grid;
            grid-template-columns: 1fr auto;
            align-items: center;
            transition: transform 0.2s ease;
        }
        .schedule-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        }
        .schedule-info h3 {
            font-size: 0.95rem;
            font-weight: 600;
            margin-bottom: 3px;
        }
        .schedule-info p {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
        .schedule-status {
            font-size: 0.7rem;
            font-weight: 600;
            padding: 3px 10px;
            border-radius: 999px;
            text-transform: uppercase;
        }
        .schedule-status.enabled {
            background-color: var(--enabled-bg);
            color: var(--enabled-text);
        }
        .schedule-status.disabled {
            background-color: var(--disabled-bg);
            color: var(--disabled-text);
        }
        .no-schedules {
            text-align: center;
            color: var(--text-secondary);
            padding: 20px;
            background-color: var(--background-color);
            border-radius: 10px;
            font-size: 0.9rem;
        }
        .actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .btn {
            padding: 8px 14px;
            border: none;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        .btn:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        .drawer {
            position: fixed;
            top: 0;
            right: -100%;
            width: 350px;
            height: 100%;
            background-color: var(--surface-color);
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
            transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }
        .drawer.open {
            right: 0;
        }
        .drawer-header {
            padding: 14px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--surface-color);
        }
        .drawer-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
        }
        .close {
            font-size: 22px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: color 0.2s ease;
        }
        .close:hover {
            color: var(--text-primary);
        }
        .drawer-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        .drawer-footer {
            padding: 14px 20px;
            border-top: 1px solid var(--border-color);
            text-align: right;
            background-color: var(--surface-color);
        }
        .form-group {
            margin-bottom: 14px;
        }
        .form-group label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        .form-group input[type="text"],
        .form-group input[type="time"],
        .form-group input[type="number"] {
            width: 100%;
            padding: 7px 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.2s ease;
        }
        .form-group input[type="number"] {
            width: 80px;
            text-align: center;
        }
        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
        .form-group input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: var(--primary-color);
            cursor: pointer;
            margin-right: 6px;
        }
        .days-selector {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 6px;
        }
        .day-checkbox {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 7px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: var(--background-color);
        }
        .day-checkbox:hover {
            border-color: var(--primary-color);
            background-color: #eff6ff;
        }
        .day-checkbox input[type="checkbox"] {
            display: none;
        }
        .day-checkbox:has(input:checked) {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        .day-checkbox span {
            font-size: 0.8rem;
            font-weight: 500;
        }
        input::placeholder {
            color: var(--text-secondary);
        }
        @media (max-width: 768px) {
            .container {
                padding: 14px;
            }
            .drawer {
                width: 100%;
            }
            .days-selector {
                grid-template-columns: repeat(3, 1fr);
            }
            .btn {
                width: 100%;
            }
            .actions {
                flex-direction: column;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>自动关机设置</h1>
            <p>管理您的关机计划</p>
        </header>
        <main>
            <section class="schedules-section">
                <div class="section-header">
                    <h2>关机计划</h2>
                    <button id="add-schedule-btn" class="btn btn-primary">添加计划</button>
                </div>
                <div id="schedules-list" class="schedules-list">
                    <div class="no-schedules">暂无计划，点击“添加计划”开始。</div>
                </div>
            </section>
            <div class="actions">
                <button id="settings-btn" class="btn btn-primary">通用设置</button>
            </div>
        </main>
    </div>
    <div id="schedule-drawer" class="drawer">
        <div class="drawer-header">
            <h3 id="drawer-title">添加计划</h3>
            <span class="close" data-drawer="schedule-drawer">×</span>
        </div>
        <div class="drawer-body">
            <div class="form-group">
                <label for="schedule-name">计划名称:</label>
                <input type="text" id="schedule-name" placeholder="例如：工作日晚上">
            </div>
            <div class="form-group">
                <label for="schedule-time">时间:</label>
                <input type="time" id="schedule-time" value="22:00">
            </div>
            <div class="form-group">
                <label>星期:</label>
                <div class="days-selector">
                    <label class="day-checkbox">
                        <input type="checkbox" value="monday">
                        <span>周一</span>
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="tuesday">
                        <span>周二</span>
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="wednesday">
                        <span>周三</span>
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="thursday">
                        <span>周四</span>
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="friday">
                        <span>周五</span>
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="saturday">
                        <span>周六</span>
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="sunday">
                        <span>周日</span>
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label for="schedule-enabled">
                    <input type="checkbox" id="schedule-enabled" checked>
                    启用此计划
                </label>
            </div>
        </div>
        <div class="drawer-footer">
            <button id="save-schedule-btn" class="btn btn-primary">保存</button>
        </div>
    </div>
    <div id="settings-drawer" class="drawer">
        <div class="drawer-header">
            <h3>通用设置</h3>
            <span class="close" data-drawer="settings-drawer">×</span>
        </div>
        <div class="drawer-body">
            <div class="form-group">
                <label for="countdown-duration">关机倒计时 (秒):</label>
                <input type="number" id="countdown-duration" min="10" max="300" value="30">
            </div>
            <div class="form-group">
                <label for="enable-notifications">
                    <input type="checkbox" id="enable-notifications" checked>
                    启用通知
                </label>
            </div>
            <div class="form-group">
                <label for="auto-start">
                    <input type="checkbox" id="auto-start">
                    随 Windows 启动
                </label>
            </div>
        </div>
        <div class="drawer-footer">
            <button id="save-settings-btn" class="btn btn-success">保存设置</button>
        </div>
    </div>
    <script src="settings.js"></script>
</body>
</html>