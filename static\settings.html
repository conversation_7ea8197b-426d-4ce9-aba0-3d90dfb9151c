<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动关机设置</title>
    <style>
        /* CSS Reset and Basic Styles */
        :root {
            --primary-color-start: #667eea;
            --primary-color-end: #764ba2;
            --background-color: #f0f2f5;
            --card-background: #ffffff;
            --text-color-primary: #1a202c;
            --text-color-secondary: #4a5568;
            --border-color: #e2e8f0;
            --success-color: #48bb78;
            --danger-color: #f56565;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color-primary);
        }
        
        /* Main Container and Header */
        .header-bg {
            background: linear-gradient(135deg, var(--primary-color-start) 0%, var(--primary-color-end) 100%);
            padding: 40px 20px;
            text-align: center;
            color: white;
            margin-bottom: -50px; /* 让卡片向上移动，形成重叠效果 */
        }

        .header-bg h1 {
            font-size: 2.2rem;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .header-bg p {
            font-size: 1rem;
            opacity: 0.9;
            margin-top: 5px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Card Layout */
        .card {
            background: var(--card-background);
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            margin-bottom: 25px;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .card-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-color-primary);
        }

        /* Schedules List */
        .schedules-list {
            display: grid;
            gap: 15px;
        }

        .schedule-item {
            background: #fdfdff;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .schedule-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.07);
            border-color: var(--primary-color-start);
        }

        .schedule-info h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .schedule-info p {
            margin: 4px 0;
            color: var(--text-color-secondary);
            font-size: 0.9rem;
        }

        .schedule-status {
            font-weight: 600;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            text-transform: uppercase;
        }

        .schedule-status.enabled {
            background: #e6f6eb;
            color: #2f855a;
        }

        .schedule-status.disabled {
            background: #fed7d7;
            color: #c53030;
        }
        
        .schedule-actions {
            display: flex;
            gap: 10px;
        }

        .no-schedules {
            text-align: center;
            color: var(--text-color-secondary);
            font-style: italic;
            padding: 40px;
            background: #fafafa;
            border-radius: 12px;
            border: 2px dashed var(--border-color);
        }

        /* General Settings & Toggle Switch */
        .settings-list {
            display: grid;
            gap: 20px;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 10px;
        }
        
        .setting-item label, .setting-item .label-text {
            font-weight: 500;
            color: var(--text-color-primary);
        }

        .setting-item input[type="number"] {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            width: 100px;
            transition: border-color 0.2s;
        }
        
        .setting-item input[type="number"]:focus {
            outline: none;
            border-color: var(--primary-color-start);
        }

        /* Toggle Switch CSS */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 28px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--success-color);
        }

        input:checked + .slider:before {
            transform: translateX(22px);
        }

        /* Buttons & Actions */
        .actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        
        .btn {
            padding: 10px 25px;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color-start) 0%, var(--primary-color-end) 100%);
            color: white;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-secondary {
            background-color: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background-color: #cbd5e0;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        /* Modal Styles (Largely Unchanged for JS Compatibility) */
        .modal {
            display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%;
            background-color: rgba(0, 0, 0, 0.5); backdrop-filter: blur(5px);
        }
        .modal-content {
            background-color: white; margin: 5% auto; padding: 0; border-radius: 16px; width: 90%;
            max-width: 500px; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            animation: modalSlideIn 0.3s ease-out;
        }
        @keyframes modalSlideIn {
            from { opacity: 0; transform: translateY(-50px) scale(0.95); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }
        .modal-header {
            padding: 20px 25px; border-bottom: 1px solid var(--border-color); display: flex;
            justify-content: space-between; align-items: center;
        }
        .modal-header h3 { font-size: 1.3rem; font-weight: 600; color: var(--text-color-primary); }
        .close { color: #a0aec0; font-size: 28px; font-weight: bold; cursor: pointer; transition: color 0.3s ease; }
        .close:hover { color: #4a5568; }
        .modal-body { padding: 25px; }
        .modal-footer {
            padding: 20px 25px; border-top: 1px solid var(--border-color); display: flex;
            gap: 10px; justify-content: flex-end;
        }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 500; color: var(--text-color-primary); }
        .form-group input[type="text"], .form-group input[type="time"] {
            width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 8px;
            font-size: 1rem; transition: border-color 0.3s ease;
        }
        .form-group input:focus {
            outline: none; border-color: var(--primary-color-start);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .days-selector { display: grid; grid-template-columns: repeat(7, 1fr); gap: 8px; margin-top: 10px; }
        .day-checkbox {
            display: flex; flex-direction: column; align-items: center; cursor: pointer;
            padding: 10px 5px; border: 2px solid var(--border-color); border-radius: 8px;
            transition: all 0.3s ease; background: white;
        }
        .day-checkbox:hover { border-color: var(--primary-color-start); background: #f8fafc; }
        .day-checkbox input[type="checkbox"] { display: none; }
        .day-checkbox:has(input:checked) {
            background: linear-gradient(135deg, var(--primary-color-start) 0%, var(--primary-color-end) 100%);
            border-color: var(--primary-color-start); color: white;
        }
        .day-checkbox span { font-size: 0.9rem; font-weight: 600; transition: color 0.3s ease; }
        .day-checkbox:has(input:checked) span { color: white; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-bg { padding: 30px 15px; }
            .container { padding: 15px; }
            .card { padding: 20px; }
            .schedule-item { flex-direction: column; align-items: flex-start; gap: 15px; }
            .schedule-actions { width: 100%; justify-content: flex-end; }
            .days-selector { grid-template-columns: repeat(4, 1fr); }
            .actions { flex-direction: column; }
            .btn { width: 100%; }
        }
    </style>
</head>
<body>
    
    <div class="header-bg">
        <h1>自动关机设置</h1>
        <p>管理和配置您的自动关机计划</p>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-header">
                <h2>关机计划</h2>
                <button id="add-schedule-btn" class="btn btn-primary">添加计划</button>
            </div>
            <div id="schedules-list" class="schedules-list">
                </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>通用设置</h2>
            </div>
            <div class="settings-list">
                <div class="setting-item">
                    <span class="label-text">关机前倒计时 (秒):</span>
                    <input type="number" id="countdown-duration" min="10" max="300" value="30">
                </div>
                <div class="setting-item">
                    <span class="label-text">启用通知</span>
                    <label class="toggle-switch">
                        <input type="checkbox" id="enable-notifications" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                <div class="setting-item">
                    <span class="label-text">随系统启动</span>
                    <label class="toggle-switch">
                        <input type="checkbox" id="auto-start">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
            
            <div class="actions">
                <button id="cancel-btn" class="btn btn-secondary">取消</button>
                <button id="save-btn" class="btn btn-success">保存设置</button>
            </div>
        </div>
    </div>

    <div id="schedule-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">添加计划</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="schedule-name">计划名称:</label>
                    <input type="text" id="schedule-name" placeholder="例如：工作日晚上">
                </div>
                <div class="form-group">
                    <label for="schedule-time">时间:</label>
                    <input type="time" id="schedule-time" value="22:00">
                </div>
                <div class="form-group">
                    <label>星期:</label>
                    <div class="days-selector">
                        <label class="day-checkbox">
                            <input type="checkbox" value="monday"><span>一</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="tuesday"><span>二</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="wednesday"><span>三</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="thursday"><span>四</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="friday"><span>五</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="saturday"><span>六</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="sunday"><span>日</span>
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="setting-item" style="padding: 0; background: none;">
                        <span class="label-text">启用此计划</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="schedule-enabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancel-schedule-btn" class="btn btn-secondary">取消</button>
                <button id="save-schedule-btn" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <script src="settings.js"></script>
</body>
</html>