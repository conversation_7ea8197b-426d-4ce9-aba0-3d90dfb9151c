# 全屏倒计时窗口白屏优化方案

## 问题描述
全屏倒计时窗口启动时会出现短暂的白屏，影响用户体验。

## 优化方案

### 1. 预设背景色
- 在 `body` 元素中预设了与主界面相同的渐变背景色
- 避免了初始的白色背景显示

### 2. Loading 状态显示
- 添加了专门的 loading 覆盖层，包含：
  - 旋转的加载动画
  - "Initializing Shutdown Warning..." 提示文字
  - 与主界面一致的背景样式

### 3. 渐进式内容显示
- 主要内容初始状态为隐藏（opacity: 0）
- 等待所有初始化完成后再显示主要内容
- 使用平滑的过渡动画

### 4. 优化的初始化流程
- JavaScript 在 DOM 加载完成后立即显示 loading 状态
- 异步获取配置信息
- 完成所有初始化后再隐藏 loading 并显示主界面
- 通过 `window_loaded` API 通知 Python 端开始倒计时

### 5. pywebview 优化设置
- 设置了 `background_color='#1a1a2e'` 避免白色闪烁
- 关闭了 debug 模式提高性能
- 使用文件协议而非 HTTP 服务器提高加载速度

## 技术实现

### HTML 改动
1. 在 `body` 中添加预设背景色
2. 添加 loading 覆盖层结构
3. 为主要内容添加 `ready` 状态类

### CSS 改动
1. 添加 loading 相关样式
2. 添加旋转动画
3. 优化内容显示的过渡效果

### JavaScript 改动
1. 重构初始化流程
2. 添加 loading 状态管理方法
3. 实现渐进式内容显示

### Python 改动
1. 添加 `window_loaded` API 方法
2. 优化 pywebview 窗口创建参数
3. 改进倒计时启动时机

## 用户体验改进
- ✅ 消除了启动时的白屏闪烁
- ✅ 提供了清晰的加载状态反馈
- ✅ 平滑的界面过渡动画
- ✅ 更快的初始化速度

## 测试方法
运行 `test_shutdown_overlay.py` 来测试优化效果。

## 兼容性
- 保持了原有的所有功能
- 向后兼容现有配置
- 支持所有原有的交互方式
